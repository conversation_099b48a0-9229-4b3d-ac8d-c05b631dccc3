"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ShareSOAPDialog } from "./share-soap-dialog";
import {
  FileText,
  Download,
  Share,
  Star,
  Clock,
  CheckCircle,
  AlertTriangle,
  ExternalLink,
  Printer,
  Calendar,
  User,
  Stethoscope,
  Brain,
  Shield,
  ShieldAlert
} from "lucide-react";
import { SOAPUtils } from "@/app/patient/_components/soap-history/types";
import { cn } from "@/lib/utils";

interface SOAPNote {
  _id: string;
  subjective: string;
  objective: string;
  assessment: string;
  plan: string;
  highlightedHtml?: string;
  qualityScore?: number;
  processingTime?: string;
  recommendations?: string[];
  googleDocUrl?: string;
  createdAt: number;
  updatedAt: number;
}

interface SOAPDocumentViewProps {
  soapNote: SOAPNote;
  patientName?: string;
  showActions?: boolean;
}

export function SOAPDocumentView({
  soapNote,
  patientName,
  showActions = true
}: SOAPDocumentViewProps) {
  const [shareDialogOpen, setShareDialogOpen] = useState(false);

  // Extract enhanced data using utility functions
  const subjective = SOAPUtils.getSubjective(soapNote);
  const objective = SOAPUtils.getObjective(soapNote);
  const assessment = SOAPUtils.getAssessment(soapNote);
  const plan = SOAPUtils.getPlan(soapNote);
  const qualityScore = SOAPUtils.getQualityScore(soapNote);
  const specialty = SOAPUtils.getSpecialty(soapNote);
  const safetyStatus = SOAPUtils.getSafetyStatus(soapNote);
  const redFlags = SOAPUtils.getRedFlags(soapNote);
  const recommendations = SOAPUtils.getRecommendations(soapNote);
  const hasEnhancedData = SOAPUtils.hasEnhancedData(soapNote);
  const chiefComplaint = SOAPUtils.getChiefComplaint(soapNote);
  const primaryDiagnosis = SOAPUtils.getPrimaryDiagnosis(soapNote);
  const processingTime = SOAPUtils.getProcessingTime(soapNote);
  const sessionId = SOAPUtils.getSessionId(soapNote);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getQualityColor = (score?: number) => {
    if (!score) return "bg-gray-100 text-gray-800";
    if (score >= 90) return "bg-green-100 text-green-800 border-green-200";
    if (score >= 80) return "bg-blue-100 text-blue-800 border-blue-200";
    if (score >= 70) return "bg-yellow-100 text-yellow-800 border-yellow-200";
    return "bg-red-100 text-red-800 border-red-200";
  };

  const handleDownload = () => {
    const content = `
SOAP CLINICAL NOTES
Generated on: ${formatDate(soapNote.createdAt)}
${patientName ? `Patient: ${patientName}` : ''}
${sessionId ? `Session ID: ${sessionId}` : ''}
${specialty ? `Specialty: ${specialty}` : ''}
${qualityScore ? `Quality Score: ${qualityScore}%` : ''}
${processingTime ? `Processing Time: ${processingTime}` : ''}
${safetyStatus !== undefined ? `Safety Status: ${safetyStatus ? 'Safe' : 'Requires Attention'}` : ''}
${hasEnhancedData ? 'Enhanced AI Analysis: Available' : ''}

${chiefComplaint ? `CHIEF COMPLAINT:\n${chiefComplaint}\n\n` : ''}SUBJECTIVE:
${subjective}

OBJECTIVE:
${objective}

ASSESSMENT:
${assessment}

PLAN:
${plan}

${recommendations.length > 0 ? `RECOMMENDATIONS:\n${recommendations.map(rec => `• ${rec}`).join('\n')}\n\n` : ''}${redFlags.length > 0 ? `RED FLAGS:\n${redFlags.map(flag => `⚠ ${flag}`).join('\n')}\n\n` : ''}Generated by MedScribe AI Assistant
    `.trim();

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `soap-notes-${new Date(soapNote.createdAt).toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="max-w-5xl mx-auto space-y-6 print:space-y-4">
      {/* Enhanced Document Header */}
      <div className="bg-white dark:bg-card border border-border rounded-2xl shadow-xl print:shadow-none print:border-0 overflow-hidden">
        <div className="border-b border-border bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-950/30 dark:via-indigo-950/30 dark:to-purple-950/30 p-8 print:bg-white print:p-6">
          <div className="flex items-start justify-between">
            <div className="space-y-3">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl shadow-lg print:bg-gray-600">
                  <FileText className="h-7 w-7 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 print:text-black">
                    SOAP Clinical Notes
                  </h1>
                  <p className="text-base text-gray-600 dark:text-gray-400 print:text-gray-600 font-medium">
                    Structured Objective Assessment & Plan
                  </p>
                </div>
              </div>

              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 dark:text-gray-400 print:text-gray-600">
                <div className="flex items-center gap-2 bg-white/60 dark:bg-gray-800/60 px-3 py-1.5 rounded-lg">
                  <Calendar className="h-4 w-4" />
                  <span className="font-medium">{formatDate(soapNote.createdAt)}</span>
                </div>
                {patientName && (
                  <div className="flex items-center gap-2 bg-white/60 dark:bg-gray-800/60 px-3 py-1.5 rounded-lg">
                    <User className="h-4 w-4" />
                    <span className="font-medium">{patientName}</span>
                  </div>
                )}
                {processingTime && (
                  <div className="flex items-center gap-2 bg-white/60 dark:bg-gray-800/60 px-3 py-1.5 rounded-lg">
                    <Clock className="h-4 w-4" />
                    <span className="font-medium">Generated in {processingTime}</span>
                  </div>
                )}
                {specialty && (
                  <div className="flex items-center gap-2 bg-white/60 dark:bg-gray-800/60 px-3 py-1.5 rounded-lg">
                    <Stethoscope className="h-4 w-4" />
                    <span className="font-medium">{specialty}</span>
                  </div>
                )}
                {hasEnhancedData && (
                  <div className="flex items-center gap-2 bg-purple-100/60 dark:bg-purple-900/60 px-3 py-1.5 rounded-lg">
                    <Brain className="h-4 w-4 text-purple-600" />
                    <span className="font-medium text-purple-700 dark:text-purple-300">AI Enhanced</span>
                  </div>
                )}
              </div>
            </div>

            <div className="flex flex-col items-end gap-3 print:hidden">
              {/* Safety Status Badge */}
              {safetyStatus !== undefined && (
                <Badge className={cn(
                  "border text-sm px-3 py-1",
                  safetyStatus
                    ? "bg-green-100 text-green-800 border-green-200"
                    : "bg-red-100 text-red-800 border-red-200"
                )}>
                  {safetyStatus ? <Shield className="h-3 w-3 mr-1" /> : <ShieldAlert className="h-3 w-3 mr-1" />}
                  {safetyStatus ? 'Safe' : 'Alert'}
                </Badge>
              )}

              {/* Quality Score Badge */}
              {qualityScore && (
                <Badge className={cn("border text-sm px-3 py-1", getQualityColor(qualityScore))}>
                  Quality: {qualityScore}%
                </Badge>
              )}

              {/* Red Flags Badge */}
              {redFlags.length > 0 && (
                <Badge className="border text-sm px-3 py-1 bg-orange-100 text-orange-800 border-orange-200">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  {redFlags.length} Flag{redFlags.length > 1 ? 's' : ''}
                </Badge>
              )}

              <div className="text-xs text-gray-500 dark:text-gray-400">
                ID: {soapNote._id.slice(-8)}
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Actions Bar
        {showActions && (
          <div className="border-b border-border p-6 bg-gradient-to-r from-gray-50/80 to-gray-100/80 dark:from-gray-900/40 dark:to-gray-800/40 print:hidden">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div className="flex flex-wrap items-center gap-3">
                <Button onClick={handleDownload} variant="outline" size="sm" className="shadow-sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download TXT
                </Button>
                <Button onClick={handlePrint} variant="outline" size="sm" className="shadow-sm">
                  <Printer className="h-4 w-4 mr-2" />
                  Print Document
                </Button>
                {soapNote.googleDocUrl && (
                  <Button variant="outline" size="sm" className="shadow-sm" asChild>
                    <a href={soapNote.googleDocUrl} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View PDF
                    </a>
                  </Button>
                )}
              </div>
              <Button
                onClick={() => setShareDialogOpen(true)}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg"
                size="sm"
              >
                <Share className="h-4 w-4 mr-2" />
                Share with Doctor
              </Button>
            </div>
          </div>
        )} */}

        {/* Enhanced Document Content */}
        <div className="p-10 print:p-8 space-y-10 print:space-y-8 bg-gradient-to-b from-white to-gray-50/30 dark:from-card dark:to-card/50">
          {/* Enhanced Data Indicator */}
          {hasEnhancedData && (
            <div className="p-4 bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800 rounded-lg">
              <div className="flex items-center gap-2 text-purple-700 dark:text-purple-300">
                <Brain className="h-5 w-5" />
                <span className="font-semibold">AI Enhanced Analysis Available</span>
              </div>
              <p className="text-sm text-purple-600 dark:text-purple-400 mt-1">
                This SOAP note includes advanced AI analysis with structured medical data, quality metrics, and safety assessments.
              </p>
            </div>
          )}

          {/* Chief Complaint (if available from enhanced data) */}
          {chiefComplaint && (
            <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h3 className="text-lg font-semibold text-blue-700 dark:text-blue-300 mb-2 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Chief Complaint
              </h3>
              <p className="text-blue-600 dark:text-blue-400 font-medium">
                {chiefComplaint}
              </p>
            </div>
          )}

          {/* Subjective Section */}
          <section className="space-y-4">
            <div className="flex items-center gap-4 border-b-2 border-blue-200 dark:border-blue-800 pb-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-xl flex items-center justify-center text-lg font-bold shadow-lg print:bg-gray-600">
                S
              </div>
              <h2 className="text-2xl font-bold text-blue-700 dark:text-blue-400 print:text-black">
                SUBJECTIVE
              </h2>
              <div className="flex-1 border-b-2 border-blue-200 dark:border-blue-800"></div>
            </div>
            <div className="pl-14 bg-blue-50/30 dark:bg-blue-950/20 rounded-xl p-6 print:bg-transparent print:p-0">
              <div className="prose prose-base max-w-none dark:prose-invert print:prose-black">
                <p className="text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap print:text-black font-medium">
                  {subjective}
                </p>
              </div>
            </div>
          </section>

          {/* Objective Section */}
          <section className="space-y-4">
            <div className="flex items-center gap-4 border-b-2 border-green-200 dark:border-green-800 pb-3">
              <div className="w-10 h-10 bg-gradient-to-br from-green-600 to-green-700 text-white rounded-xl flex items-center justify-center text-lg font-bold shadow-lg print:bg-gray-600">
                O
              </div>
              <h2 className="text-2xl font-bold text-green-700 dark:text-green-400 print:text-black">
                OBJECTIVE
              </h2>
              <div className="flex-1 border-b-2 border-green-200 dark:border-green-800"></div>
            </div>
            <div className="pl-14 bg-green-50/30 dark:bg-green-950/20 rounded-xl p-6 print:bg-transparent print:p-0">
              <div className="prose prose-base max-w-none dark:prose-invert print:prose-black">
                <p className="text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap print:text-black font-medium">
                  {objective}
                </p>
              </div>
            </div>
          </section>

          {/* Assessment Section */}
          <section className="space-y-4">
            <div className="flex items-center gap-4 border-b-2 border-orange-200 dark:border-orange-800 pb-3">
              <div className="w-10 h-10 bg-gradient-to-br from-orange-600 to-orange-700 text-white rounded-xl flex items-center justify-center text-lg font-bold shadow-lg print:bg-gray-600">
                A
              </div>
              <h2 className="text-2xl font-bold text-orange-700 dark:text-orange-400 print:text-black">
                ASSESSMENT
              </h2>
              <div className="flex-1 border-b-2 border-orange-200 dark:border-orange-800"></div>
            </div>
            <div className="pl-14 bg-orange-50/30 dark:bg-orange-950/20 rounded-xl p-6 print:bg-transparent print:p-0">
              <div className="prose prose-base max-w-none dark:prose-invert print:prose-black">
                <p className="text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap print:text-black font-medium">
                  {assessment}
                </p>
              </div>

              {/* Primary Diagnosis (if available from enhanced data) */}
              {primaryDiagnosis && (
                <div className="mt-4 p-4 bg-orange-100 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                  <h4 className="font-semibold text-orange-700 dark:text-orange-300 mb-2">Primary Diagnosis</h4>
                  <p className="text-orange-600 dark:text-orange-400 font-medium">
                    {primaryDiagnosis}
                  </p>
                </div>
              )}
            </div>
          </section>

          {/* Plan Section */}
          <section className="space-y-4">
            <div className="flex items-center gap-4 border-b-2 border-purple-200 dark:border-purple-800 pb-3">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-purple-700 text-white rounded-xl flex items-center justify-center text-lg font-bold shadow-lg print:bg-gray-600">
                P
              </div>
              <h2 className="text-2xl font-bold text-purple-700 dark:text-purple-400 print:text-black">
                PLAN
              </h2>
              <div className="flex-1 border-b-2 border-purple-200 dark:border-purple-800"></div>
            </div>
            <div className="pl-14 bg-purple-50/30 dark:bg-purple-950/20 rounded-xl p-6 print:bg-transparent print:p-0">
              <div className="prose prose-base max-w-none dark:prose-invert print:prose-black">
                <p className="text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap print:text-black font-medium">
                  {plan}
                </p>
              </div>
            </div>
          </section>

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <section className="space-y-3 border-t border-gray-200 pt-6 print:border-gray-400">
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-5 w-5 text-amber-600" />
                <h3 className="text-lg font-semibold text-amber-700 dark:text-amber-400 print:text-black">
                  AI Recommendations
                </h3>
              </div>
              <div className="pl-8 space-y-2">
                {recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0 print:text-gray-600" />
                    <span className="text-sm text-gray-700 dark:text-gray-300 print:text-black">
                      {recommendation}
                    </span>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Red Flags Section */}
          {redFlags.length > 0 && (
            <section className="space-y-4">
              <div className="flex items-center gap-4 border-b-2 border-red-200 dark:border-red-800 pb-3">
                <div className="w-10 h-10 bg-gradient-to-br from-red-600 to-red-700 text-white rounded-xl flex items-center justify-center text-lg font-bold shadow-lg print:bg-gray-600">
                  ⚠
                </div>
                <h2 className="text-2xl font-bold text-red-700 dark:text-red-400 print:text-black">
                  RED FLAGS
                </h2>
                <div className="flex-1 border-b-2 border-red-200 dark:border-red-800"></div>
              </div>
              <div className="pl-14 bg-red-50/30 dark:bg-red-950/20 rounded-xl p-6 print:bg-transparent print:p-0">
                <div className="space-y-3">
                  {redFlags.map((flag, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-red-100 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                      <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                      <span className="text-red-700 dark:text-red-300 font-medium leading-relaxed">
                        {flag}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </section>
          )}

          {/* Footer */}
          <div className="border-t border-gray-200 pt-6 mt-8 print:border-gray-400">
            <div className="flex items-center justify-between text-xs text-gray-500 print:text-gray-600">
              <div className="flex items-center gap-2">
                <Stethoscope className="h-4 w-4" />
                <span>Generated by MedScribe AI Assistant</span>
              </div>
              <div>
                Document ID: {soapNote._id.slice(-8)}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Share Dialog */}
      <ShareSOAPDialog
        open={shareDialogOpen}
        onOpenChange={setShareDialogOpen}
        soapNoteId={soapNote._id}
        onSuccess={() => {
          // Could add a success callback here
        }}
      />
    </div>
  );
}

// "use client";

// import React, { useState } from "react";
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
// import { Button } from "@/components/ui/button";
// import { Badge } from "@/components/ui/badge";
// import { Separator } from "@/components/ui/separator";
// import { ShareSOAPDialog } from "./share-soap-dialog";
// import {
//   FileText,
//   Download,
//   Share,
//   Star,
//   Clock,
//   CheckCircle,
//   AlertTriangle,
//   ExternalLink,
//   Printer,
//   Calendar,
//   User,
//   Stethoscope,
//   Activity
// } from "lucide-react";
// import { cn } from "@/lib/utils";

// interface SOAPNote {
//   _id: string;
//   subjective: string;
//   objective: string;
//   assessment: string;
//   plan: string;
//   highlightedHtml?: string;
//   qualityScore?: number;
//   processingTime?: string;
//   recommendations?: string[];
//   googleDocUrl?: string;
//   createdAt: number;
//   updatedAt: number;
// }

// interface SOAPDocumentViewProps {
//   soapNote: SOAPNote;
//   patientName?: string;
//   showActions?: boolean;
// }

// export function SOAPDocumentView({
//   soapNote,
//   patientName,
//   showActions = true
// }: SOAPDocumentViewProps) {
//   const [shareDialogOpen, setShareDialogOpen] = useState(false);

//   const formatDate = (timestamp: number) => {
//     return new Date(timestamp).toLocaleDateString('en-US', {
//       year: 'numeric',
//       month: 'long',
//       day: 'numeric',
//       hour: '2-digit',
//       minute: '2-digit'
//     });
//   };

//   const getQualityVariant = (score?: number) => {
//     if (!score) return "secondary";
//     if (score >= 90) return "default"; // Uses primary color
//     if (score >= 80) return "secondary";
//     if (score >= 70) return "outline";
//     return "destructive";
//   };

//   const getQualityLabel = (score?: number) => {
//     if (!score) return "Not Rated";
//     if (score >= 90) return "Excellent";
//     if (score >= 80) return "Good";
//     if (score >= 70) return "Fair";
//     return "Needs Review";
//   };

//   const handleDownload = () => {
//     const content = `
// SOAP CLINICAL NOTES
// Generated on: ${formatDate(soapNote.createdAt)}
// ${patientName ? `Patient: ${patientName}` : ''}
// ${soapNote.qualityScore ? `Quality Score: ${soapNote.qualityScore}%` : ''}
// ${soapNote.processingTime ? `Processing Time: ${soapNote.processingTime}` : ''}

// SUBJECTIVE:
// ${soapNote.subjective}

// OBJECTIVE:
// ${soapNote.objective}

// ASSESSMENT:
// ${soapNote.assessment}

// PLAN:
// ${soapNote.plan}

// ${soapNote.recommendations?.length ? `
// RECOMMENDATIONS:
// ${soapNote.recommendations.map(rec => `• ${rec}`).join('\n')}
// ` : ''}

// Generated by MedScribe AI Assistant
//     `.trim();

//     const blob = new Blob([content], { type: 'text/plain' });
//     const url = URL.createObjectURL(blob);
//     const a = document.createElement('a');
//     a.href = url;
//     a.download = `soap-notes-${new Date(soapNote.createdAt).toISOString().split('T')[0]}.txt`;
//     document.body.appendChild(a);
//     a.click();
//     document.body.removeChild(a);
//     URL.revokeObjectURL(url);
//   };

//   const handlePrint = () => {
//     window.print();
//   };

//   return (
//     <div className="max-w-4xl mx-auto space-y-6">
//       {/* Document Header */}
//       <Card className="print:shadow-none print:border-0">
//         <CardHeader className="space-y-4">
//           <div className="flex items-start justify-between">
//             <div className="space-y-3">
//               <div className="flex items-center gap-3">
//                 <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground">
//                   <FileText className="h-5 w-5" />
//                 </div>
//                 <div>
//                   <CardTitle className="text-2xl">SOAP Clinical Notes</CardTitle>
//                   <CardDescription className="text-base">
//                     Structured Objective Assessment & Plan
//                   </CardDescription>
//                 </div>
//               </div>

//               <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
//                 <div className="flex items-center gap-2">
//                   <Calendar className="h-4 w-4" />
//                   <span>{formatDate(soapNote.createdAt)}</span>
//                 </div>
//                 {patientName && (
//                   <div className="flex items-center gap-2">
//                     <User className="h-4 w-4" />
//                     <span>{patientName}</span>
//                   </div>
//                 )}
//                 {soapNote.processingTime && (
//                   <div className="flex items-center gap-2">
//                     <Clock className="h-4 w-4" />
//                     <span>Generated in {soapNote.processingTime}</span>
//                   </div>
//                 )}
//               </div>
//             </div>

//             <div className="flex flex-col items-end gap-2 print:hidden">
//               {soapNote.qualityScore && (
//                 <Badge variant={getQualityVariant(soapNote.qualityScore)}>
//                   {getQualityLabel(soapNote.qualityScore)} ({soapNote.qualityScore}%)
//                 </Badge>
//               )}
//               <span className="text-xs text-muted-foreground">
//                 ID: {soapNote._id.slice(-8)}
//               </span>
//             </div>
//           </div>
//         </CardHeader>

//         {/* Actions Bar */}
//         {showActions && (
//           <>
//             <Separator />
//             <CardContent className="pt-6 print:hidden">
//               <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
//                 <div className="flex flex-wrap items-center gap-2">
//                   <Button onClick={handleDownload} variant="outline" size="sm">
//                     <Download className="h-4 w-4 mr-2" />
//                     Download
//                   </Button>
//                   <Button onClick={handlePrint} variant="outline" size="sm">
//                     <Printer className="h-4 w-4 mr-2" />
//                     Print
//                   </Button>
//                   {soapNote.googleDocUrl && (
//                     <Button variant="outline" size="sm" asChild>
//                       <a href={soapNote.googleDocUrl} target="_blank" rel="noopener noreferrer">
//                         <ExternalLink className="h-4 w-4 mr-2" />
//                         View PDF
//                       </a>
//                     </Button>
//                   )}
//                 </div>
//                 <Button
//                   onClick={() => setShareDialogOpen(true)}
//                   size="sm"
//                 >
//                   <Share className="h-4 w-4 mr-2" />
//                   Share with Doctor
//                 </Button>
//               </div>
//             </CardContent>
//           </>
//         )}
//       </Card>

//       {/* SOAP Sections */}
//       <div className="space-y-6">
//         {/* Subjective Section */}
//         <Card>
//           <CardHeader>
//             <div className="flex items-center gap-3">
//               <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground text-sm font-semibold">
//                 S
//               </div>
//               <CardTitle className="text-xl">Subjective</CardTitle>
//             </div>
//           </CardHeader>
//           <CardContent>
//             <div className="prose prose-sm max-w-none dark:prose-invert">
//               <p className="whitespace-pre-wrap leading-relaxed text-foreground">
//                 {soapNote.subjective}
//               </p>
//             </div>
//           </CardContent>
//         </Card>

//         {/* Objective Section */}
//         <Card>
//           <CardHeader>
//             <div className="flex items-center gap-3">
//               <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground text-sm font-semibold">
//                 O
//               </div>
//               <CardTitle className="text-xl">Objective</CardTitle>
//             </div>
//           </CardHeader>
//           <CardContent>
//             <div className="prose prose-sm max-w-none dark:prose-invert">
//               <p className="whitespace-pre-wrap leading-relaxed text-foreground">
//                 {soapNote.objective}
//               </p>
//             </div>
//           </CardContent>
//         </Card>

//         {/* Assessment Section */}
//         <Card>
//           <CardHeader>
//             <div className="flex items-center gap-3">
//               <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground text-sm font-semibold">
//                 A
//               </div>
//               <CardTitle className="text-xl">Assessment</CardTitle>
//             </div>
//           </CardHeader>
//           <CardContent>
//             <div className="prose prose-sm max-w-none dark:prose-invert">
//               <p className="whitespace-pre-wrap leading-relaxed text-foreground">
//                 {soapNote.assessment}
//               </p>
//             </div>
//           </CardContent>
//         </Card>

//         {/* Plan Section */}
//         <Card>
//           <CardHeader>
//             <div className="flex items-center gap-3">
//               <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary text-primary-foreground text-sm font-semibold">
//                 P
//               </div>
//               <CardTitle className="text-xl">Plan</CardTitle>
//             </div>
//           </CardHeader>
//           <CardContent>
//             <div className="prose prose-sm max-w-none dark:prose-invert">
//               <p className="whitespace-pre-wrap leading-relaxed text-foreground">
//                 {soapNote.plan}
//               </p>
//             </div>
//           </CardContent>
//         </Card>

//         {/* Recommendations */}
//         {soapNote.recommendations && soapNote.recommendations.length > 0 && (
//           <Card>
//             <CardHeader>
//               <div className="flex items-center gap-3">
//                 <div className="flex h-8 w-8 items-center justify-center rounded-md bg-secondary text-secondary-foreground">
//                   <Activity className="h-4 w-4" />
//                 </div>
//                 <CardTitle className="text-xl">AI Recommendations</CardTitle>
//               </div>
//             </CardHeader>
//             <CardContent>
//               <div className="space-y-3">
//                 {soapNote.recommendations.map((recommendation, index) => (
//                   <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-muted/50">
//                     <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
//                     <span className="text-sm text-foreground leading-relaxed">
//                       {recommendation}
//                     </span>
//                   </div>
//                 ))}
//               </div>
//             </CardContent>
//           </Card>
//         )}
//       </div>

//       {/* Footer Information */}
//       <Card className="print:hidden">
//         <CardContent className="pt-6">
//           <div className="flex items-center justify-between text-sm text-muted-foreground">
//             <div className="flex items-center gap-2">
//               <Stethoscope className="h-4 w-4" />
//               <span>Generated by MedScribe AI Assistant</span>
//             </div>
//             <div className="flex items-center gap-4">
//               <span>Document ID: {soapNote._id.slice(-8)}</span>
//               <span>Last updated: {formatDate(soapNote.updatedAt)}</span>
//             </div>
//           </div>
//         </CardContent>
//       </Card>

//       {/* Share Dialog */}
//       <ShareSOAPDialog
//         open={shareDialogOpen}
//         onOpenChange={setShareDialogOpen}
//         soapNoteId={soapNote._id}
//         onSuccess={() => {
//           // Could add a success callback here
//         }}
//       />
//     </div>
//   );
// }