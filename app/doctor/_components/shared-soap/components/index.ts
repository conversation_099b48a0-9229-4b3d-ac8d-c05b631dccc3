// Export all shared SOAP components
export { SharedSOAPSkeleton } from './SharedSOAPSkeleton';
export { SharedSOAPFilters } from './SharedSOAPFilters';
export { SharedSOAPNoteCard } from './SharedSOAPNoteCard';
export { SharedSOAPNotesList } from './SharedSOAPNotesList';
export { TakeActionDialog } from './TakeActionDialog';

// Re-export component prop types for convenience
export type {
  SharedSOAPFiltersProps,
  SharedSOAPNotesListProps,
  SharedSOAPNoteCardProps,
} from '../types';
